import { useParams, useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';
import Button from '@/components/ui/Button';
import { createWidget } from '@/services/dashboardService';
import { CreateWidgetPayload } from '@/types/dashboard';

// Define the mapping between report types and their allowed chart types
const REPORT_CHART_MAPPING = {
  'process': ['bar_stack', 'bar_comp', 'line', 'table'],
  'top_sources': ['table', 'bar_stack_hor', 'bar_stack_ver', 'radial'],
  'sentiment_analysis': ['pie', 'donut', 'semi_pie', 'bar_stack_hor', 'bar_stack_ver', 'table', 'radar', 'spider', 'wind']
} as const;

export default function CreateReport() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedSocials, setSelectedSocials] = useState<number[]>([1]);
  const [selectedReport, setSelectedReport] = useState<string>('');
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [selectedTimeInterval, setSelectedTimeInterval] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'داشبورد جزئیات', href: `/dashboard/${id}` },
    { label: 'ایجاد گزارش جدید' },
  ];

  // Define report options
  const reportOptions: SelectOption[] = [
    { label: 'روند انتشار محتوا طی زمان', value: 'process' },
    { label: 'برترین منابع (کاربران)', value: 'top_sources' },
    { label: 'تحلیل احساسات محتوای منتشر شده', value: 'sentiment_analysis' },
  ];

  // Define chart type options with Persian labels
  const chartTypeLabels: Record<string, string> = {
    'bar_stack': 'نمودار میله‌ای انباشته',
    'bar_comp': 'نمودار میله‌ای مقایسه‌ای',
    'line': 'نمودار خطی',
    'table': 'جدول',
    'bar_stack_hor': 'نمودار میله‌ای انباشته افقی',
    'bar_stack_ver': 'نمودار میله‌ای انباشته عمودی',
    'radial': 'نمودار شعاعی',
    'pie': 'نمودار دایره‌ای',
    'donut': 'نمودار حلقه‌ای',
    'semi_pie': 'نمودار نیم‌دایره',
    'radar': 'نمودار راداری',
    'spider': 'نمودار عنکبوتی',
    'wind': 'نمودار بادی'
  };

  // Define time interval options (in minutes)
  const timeIntervalOptions: SelectOption[] = [
    { label: '1 دقیقه', value: '1' },
    { label: '3 دقیقه', value: '3' },
    { label: '5 دقیقه', value: '5' },
    { label: '15 دقیقه', value: '15' },
    { label: '30 دقیقه', value: '30' },
    { label: '45 دقیقه', value: '45' },
    { label: '60 دقیقه', value: '60' },
    { label: '90 دقیقه', value: '90' },
    { label: '120 دقیقه', value: '120' },
  ];

  // Generate chart type options based on selected report
  const getChartTypeOptions = (): SelectOption[] => {
    if (!selectedReport || !(selectedReport in REPORT_CHART_MAPPING)) {
      return [];
    }

    const allowedChartTypes = REPORT_CHART_MAPPING[selectedReport as keyof typeof REPORT_CHART_MAPPING];
    return allowedChartTypes.map(chartType => ({
      label: chartTypeLabels[chartType],
      value: chartType
    }));
  };

  const handleSocialMediaSelectChange = (selectedIds: number[]) => {
    setSelectedSocials(selectedIds);
    setError('');
  };

  const handleReportChange = (value: string | string[]) => {
    const reportValue = Array.isArray(value) ? value[0] : value;
    setSelectedReport(reportValue);
    // Reset chart type when report changes
    setSelectedChartType('');
  };

  const handleChartTypeChange = (value: string | string[]) => {
    const chartValue = Array.isArray(value) ? value[0] : value;
    setSelectedChartType(chartValue);
  };

  const handleTimeIntervalChange = (value: string | string[]) => {
    const intervalValue = Array.isArray(value) ? value[0] : value;
    setSelectedTimeInterval(intervalValue);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
    setError('');
  };

  const validateForm = (): boolean => {
    if (!title.trim()) {
      setError('عنوان نمودار الزامی است');
      return false;
    }
    if (!selectedReport) {
      setError('انتخاب نوع گزارش الزامی است');
      return false;
    }
    if (!selectedChartType) {
      setError('انتخاب نوع نمودار الزامی است');
      return false;
    }
    if (!selectedTimeInterval) {
      setError('انتخاب وقفه زمانی الزامی است');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !id) return;

    setIsSubmitting(true);
    setError('');

    try {
      const payload: CreateWidgetPayload = {
        title: title.trim(),
        chart_type: selectedChartType,
        report_type: selectedReport,
        params: {
          runtime: {
            interval: parseInt(selectedTimeInterval) * 60, // Convert minutes to seconds
          },
          position: {
            x: 0,
            y: 0,
            width: 5,
            height: 5,
          },
        },
      };

      await createWidget(id, payload);

      // Navigate back to dashboard detail page
      navigate(`/dashboard/${id}`);
    } catch (error) {
      console.error('Error creating widget:', error);
      setError(error instanceof Error ? error.message : 'خطا در ایجاد ویجت');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 w-full max-w-7xl space-y-6">
        {/* Social Media Select - Full Width */}
        <div className="w-full">
          <SocialMediaSelect
            mode="single"
            label="بستر‌های مورد نظر خود را انتخاب کنید"
            value={selectedSocials}
            onChange={handleSocialMediaSelectChange}
            error={error}
          />
        </div>

        {/* Text Input and Time Interval Select - Half Width Each */}
        <div className="mt-8 flex items-center gap-4">
          <div className="flex-6">
            <TextInput
              label="عنوان نمودار"
              placeholder="نمودار برترین محتوا منتشر شده در اینستاگرام"
              value={title}
              onChange={handleTitleChange}
            />
          </div>
          <div className="flex-6">
            <Select
              label="وقفه زمانی به‌روزرسانی گزارش‌ها"
              placeholder="انتخاب کنید"
              options={timeIntervalOptions}
              value={selectedTimeInterval}
              onChange={handleTimeIntervalChange}
            />
          </div>
        </div>

        {/* Report and Chart Type Selects - Half Width Each */}
        <div className="flex items-center gap-4">
          <div className="flex-6">
            <Select
              label="گزارش‌های آماری و هوش مصنوعی"
              placeholder="انتخاب نوع گزارش"
              options={reportOptions}
              value={selectedReport}
              onChange={handleReportChange}
            />
          </div>
          <div className="flex-6">
            <Select
              label="نوع نمودار"
              placeholder={selectedReport ? "انتخاب نوع نمودار" : "ابتدا نوع گزارش را انتخاب کنید"}
              options={getChartTypeOptions()}
              value={selectedChartType}
              onChange={handleChartTypeChange}
            />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-8 flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={() => navigate(`/dashboard/${id}`)}
            disabled={isSubmitting}
          >
            انصراف
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'در حال ایجاد...' : 'ایجاد ویجت'}
          </Button>
        </div>
      </div>
    </div>
  );
}
