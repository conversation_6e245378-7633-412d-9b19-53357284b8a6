import {
  InstagramLogoIcon,
  TelegramLogoIcon,
  TwitterLogoIcon,
  YoutubeLogoIcon,
} from '@phosphor-icons/react';

export type SocialItem = {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; className: string }>;
};

export const socialItems: SocialItem[] = [
  {
    id: 1,
    title: 'تلگرام',
    description: 'تحلیل داده‌هـای\nپیام رسان تلگرام',
    icon: TelegramLogoIcon,
  },
  {
    id: 2,
    title: 'اینستاگرام',
    description: 'تحلیل داده‌های \n  اینستاگرام',
    icon: InstagramLogoIcon,
  },
  {
    id: 3,
    title: 'توییتر',
    description: 'تحلیل داده‌های توییتر  (X)',
    icon: TwitterLogoIcon,
  },
  {
    id: 4,
    title: 'یوتوب',
    description: 'تحلیل داده‌های یوتوب',
    icon: YoutubeLogoIcon,
  },
];
