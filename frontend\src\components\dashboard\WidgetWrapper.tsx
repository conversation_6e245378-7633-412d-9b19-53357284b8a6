import React from 'react';
import { CHART_TYPE_MAPPING, ChartType } from '@/constants/chartTypes';
import { Widget, Dashboard } from '@/types/dashboard';
import { useWidgetData } from '@/hooks/useWidgetData';
import WidgetSkeleton from '@/components/ui/WidgetSkeleton';

interface WidgetWrapperProps {
  widget: Widget;
  dashboard: Dashboard;
  className?: string;
}

const WidgetWrapper: React.FC<WidgetWrapperProps> = ({
  widget,
  dashboard,
  className = '',
}) => {
  const { data, loading, error } = useWidgetData({ widget, dashboard });

  // Show loading skeleton while data is being fetched
  if (loading) {
    return <WidgetSkeleton className={className} />;
  }

  // Show error state
  if (error) {
    return (
      <div className={`flex h-full w-full items-center justify-center rounded-lg bg-red-900/20 p-4 ${className}`}>
        <div className="text-center text-red-400">
          <div className="mb-2 text-sm font-medium">خطا در بارگذاری داده‌ها</div>
          <div className="text-xs opacity-80">{error}</div>
        </div>
      </div>
    );
  }

  // Get the chart component
  const ChartComponent = CHART_TYPE_MAPPING[widget.chart_type as ChartType];
  
  if (!ChartComponent) {
    return (
      <div className={`flex h-full w-full items-center justify-center rounded-lg bg-gray-800/50 p-4 ${className}`}>
        <div className="text-center text-gray-400">
          <div className="text-sm font-medium">نوع نمودار پشتیبانی نمی‌شود</div>
          <div className="text-xs opacity-80">{widget.chart_type}</div>
        </div>
      </div>
    );
  }

  // Render the chart with data
  return (
    <div className={`h-full w-full p-1 ${className}`}>
      <ChartComponent
        title={widget.title}
        data={data}
        className="h-full w-full"
        {...data}
      />
    </div>
  );
};

export default WidgetWrapper;
